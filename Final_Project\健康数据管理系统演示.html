<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人健康数据管理系统 - Personal Health Data Management System</title>

    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Highlight.js CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>

    <!-- Mermaid CDN for Flowcharts -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>

    <style>
        :root {
            --primary-color: #00AEEF;
            --secondary-color: #4CAF50;
            --card-bg: #1a1a1a;
            --card-border: #333;
        }

        body {
            background: #FFFFFF;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .tech-gradient {
            background: linear-gradient(135deg, rgba(0, 174, 239, 0.7), rgba(0, 174, 239, 0.3));
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 174, 239, 0.2);
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .tech-number {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mermaid {
            background: transparent !important;
        }

        .code-card {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #0d1117;
            border: 1px solid #30363d;
        }
    </style>
</head>

<body class="bg-white">
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-green-50">
        <div class="text-center px-6">
            <h1 class="text-6xl md:text-7xl font-bold text-gray-900 mb-4 fade-in">
                个人健康数据管理系统
            </h1>
            <h2 class="text-2xl md:text-3xl text-gray-600 mb-8 fade-in">
                Personal Health Data Management System
            </h2>
            <div class="flex justify-center items-center space-x-8 mb-12 fade-in">
                <div class="text-center">
                    <i class="fas fa-heartbeat text-6xl text-red-500 mb-2"></i>
                    <p class="text-gray-700 font-medium">健康监测</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-chart-line text-6xl" style="color: var(--primary-color)"></i>
                    <p class="text-gray-700 font-medium">数据分析</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-shield-alt text-6xl" style="color: var(--secondary-color)"></i>
                    <p class="text-gray-700 font-medium">风险评估</p>
                </div>
            </div>
            <button onclick="scrollToSection('background')"
                class="bg-gradient-to-r from-blue-500 to-green-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:shadow-lg transition-all duration-300 fade-in">
                开始探索 <i class="fas fa-arrow-down ml-2"></i>
            </button>
        </div>
    </section>

    <!-- Research Background -->
    <section id="background" class="py-20 px-6">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">研究背景</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                <div class="bg-gray-900 rounded-xl p-8 text-white card-hover fade-in">
                    <i class="fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"></i>
                    <h3 class="text-2xl font-bold mb-4">健康问题现状</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li><i class="fas fa-dot-circle text-red-400 mr-2"></i>慢性病发病率持续上升</li>
                        <li><i class="fas fa-dot-circle text-red-400 mr-2"></i>个人健康数据管理缺乏系统性</li>
                        <li><i class="fas fa-dot-circle text-red-400 mr-2"></i>早期预警机制不完善</li>
                        <li><i class="fas fa-dot-circle text-red-400 mr-2"></i>健康风险评估工具匮乏</li>
                    </ul>
                </div>

                <div class="bg-gray-900 rounded-xl p-8 text-white card-hover fade-in">
                    <i class="fas fa-lightbulb text-4xl text-yellow-400 mb-4"></i>
                    <h3 class="text-2xl font-bold mb-4">解决方案需求</h3>
                    <ul class="space-y-3 text-gray-300">
                        <li><i class="fas fa-check-circle text-green-400 mr-2"></i>智能化健康数据录入</li>
                        <li><i class="fas fa-check-circle text-green-400 mr-2"></i>多维度健康状态评估</li>
                        <li><i class="fas fa-check-circle text-green-400 mr-2"></i>慢性病风险预测模型</li>
                        <li><i class="fas fa-check-circle text-green-400 mr-2"></i>个性化健康建议生成</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- System Architecture -->
    <section id="architecture" class="py-20 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">功能框架</h2>

            <div class="bg-white rounded-2xl p-8 shadow-lg mb-12 fade-in">
                <h3 class="text-3xl font-bold mb-8 text-center">系统架构流程图</h3>
                <div class="mermaid">
                    graph TD
                    A[用户输入健康数据] --> B[数据验证与存储]
                    B --> C[健康状态评估]
                    B --> D[慢性病风险分析]
                    C --> E[生成健康报告]
                    D --> F[风险预警提醒]
                    E --> G[个性化建议]
                    F --> G
                    G --> H[趋势分析]
                    H --> I[数据可视化展示]

                    style A fill:#e1f5fe
                    style B fill:#f3e5f5
                    style C fill:#e8f5e8
                    style D fill:#fff3e0
                    style E fill:#fce4ec
                    style F fill:#ffebee
                    style G fill:#e0f2f1
                    style H fill:#f1f8e9
                    style I fill:#e3f2fd
                </div>
            </div>
        </div>
    </section>

    <!-- Core Features -->
    <section id="features" class="py-20 px-6">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">核心功能模块</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                <!-- Data Input Module -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-keyboard text-4xl mb-4" style="color: var(--primary-color)"></i>
                    <h3 class="text-xl font-bold mb-2">数据录入</h3>
                    <p class="text-sm text-gray-400 mb-3">Data Input</p>
                    <p class="text-gray-300 text-sm">支持18项健康指标的智能化数据录入</p>
                </div>

                <!-- Health Assessment -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <div class="text-5xl font-bold tech-number mb-2">95%</div>
                    <h3 class="text-xl font-bold mb-2">健康评估</h3>
                    <p class="text-sm text-gray-400 mb-3">Health Assessment</p>
                    <p class="text-gray-300 text-sm">基于多维度指标的综合健康状态评估</p>
                </div>

                <!-- Risk Assessment -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-shield-alt text-4xl mb-4" style="color: var(--secondary-color)"></i>
                    <h3 class="text-xl font-bold mb-2">风险评估</h3>
                    <p class="text-sm text-gray-400 mb-3">Risk Assessment</p>
                    <p class="text-gray-300 text-sm">慢性病风险预测与早期预警</p>
                </div>

                <!-- Data Query -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <div class="text-5xl font-bold tech-number mb-2">100+</div>
                    <h3 class="text-xl font-bold mb-2">数据查询</h3>
                    <p class="text-sm text-gray-400 mb-3">Data Query</p>
                    <p class="text-gray-300 text-sm">支持多条件查询和数据导出</p>
                </div>

                <!-- Trend Analysis -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-chart-line text-4xl mb-4" style="color: var(--primary-color)"></i>
                    <h3 class="text-xl font-bold mb-2">趋势分析</h3>
                    <p class="text-sm text-gray-400 mb-3">Trend Analysis</p>
                    <p class="text-gray-300 text-sm">健康指标变化趋势智能分析</p>
                </div>

                <!-- BMI Calculator -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <div class="text-5xl font-bold tech-number mb-2">BMI</div>
                    <h3 class="text-xl font-bold mb-2">体重指数</h3>
                    <p class="text-sm text-gray-400 mb-3">Body Mass Index</p>
                    <p class="text-gray-300 text-sm">智能BMI计算与分类评估</p>
                </div>

                <!-- Blood Pressure -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-heartbeat text-4xl mb-4 text-red-400"></i>
                    <h3 class="text-xl font-bold mb-2">血压监测</h3>
                    <p class="text-sm text-gray-400 mb-3">Blood Pressure</p>
                    <p class="text-gray-300 text-sm">收缩压舒张压综合评估</p>
                </div>

                <!-- Blood Sugar -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <div class="text-5xl font-bold tech-number mb-2">HbA1c</div>
                    <h3 class="text-xl font-bold mb-2">血糖管理</h3>
                    <p class="text-sm text-gray-400 mb-3">Glucose Control</p>
                    <p class="text-gray-300 text-sm">空腹血糖与糖化血红蛋白监测</p>
                </div>

                <!-- File Management -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-file-csv text-4xl mb-4" style="color: var(--secondary-color)"></i>
                    <h3 class="text-xl font-bold mb-2">文件管理</h3>
                    <p class="text-sm text-gray-400 mb-3">File Management</p>
                    <p class="text-gray-300 text-sm">CSV格式数据导入导出</p>
                </div>

                <!-- Health Advice -->
                <div class="bg-gray-900 rounded-lg p-6 text-white card-hover fade-in">
                    <i class="fas fa-user-md text-4xl mb-4" style="color: var(--primary-color)"></i>
                    <h3 class="text-xl font-bold mb-2">健康建议</h3>
                    <p class="text-sm text-gray-400 mb-3">Health Advice</p>
                    <p class="text-gray-300 text-sm">个性化健康改善建议生成</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Implementation -->
    <section id="implementation" class="py-20 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">关键算法实现</h2>

            <!-- Health Assessment Algorithm -->
            <div class="bg-white rounded-2xl p-8 shadow-lg mb-12 fade-in">
                <h3 class="text-3xl font-bold mb-8 flex items-center">
                    <i class="fas fa-cogs mr-4" style="color: var(--primary-color)"></i>
                    健康评估算法流程
                </h3>
                <div class="mermaid">
                    graph LR
                    A[输入健康数据] --> B{数据完整性检查}
                    B -->|完整| C[BMI评估]
                    B -->|不完整| D[提示补充数据]
                    C --> E[血压评估]
                    E --> F[血糖评估]
                    F --> G[血脂评估]
                    G --> H[综合评分计算]
                    H --> I[生成健康等级]
                    I --> J[输出评估报告]

                    style A fill:#e3f2fd
                    style B fill:#fff3e0
                    style C fill:#e8f5e8
                    style D fill:#ffebee
                    style E fill:#f3e5f5
                    style F fill:#e0f2f1
                    style G fill:#fce4ec
                    style H fill:#e1f5fe
                    style I fill:#f1f8e9
                    style J fill:#e8eaf6
                </div>
            </div>

            <!-- Risk Assessment Algorithm -->
            <div class="bg-white rounded-2xl p-8 shadow-lg mb-12 fade-in">
                <h3 class="text-3xl font-bold mb-8 flex items-center">
                    <i class="fas fa-shield-alt mr-4" style="color: var(--secondary-color)"></i>
                    慢性病风险评估模型
                </h3>
                <div class="mermaid">
                    graph TD
                    A[患者基本信息] --> B[心血管风险评估]
                    A --> C[糖尿病风险评估]
                    A --> D[慢性肾病风险评估]
                    A --> E[肿瘤风险筛查]

                    B --> F[Framingham风险评分]
                    C --> G[FINDRISC评分]
                    D --> H[eGFR计算]
                    E --> I[多因子风险分析]

                    F --> J[综合风险等级]
                    G --> J
                    H --> J
                    I --> J

                    J --> K[个性化建议生成]
                    K --> L[风险预警提醒]

                    style A fill:#e3f2fd
                    style B fill:#ffebee
                    style C fill:#e8f5e8
                    style D fill:#fff3e0
                    style E fill:#f3e5f5
                    style J fill:#e1f5fe
                    style K fill:#e0f2f1
                    style L fill:#fce4ec
                </div>
            </div>
        </div>
    </section>

    <!-- Data Structure -->
    <section id="datastructure" class="py-20 px-6">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">数据结构设计</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Health Data Structure -->
                <div class="code-card rounded-xl p-6 fade-in">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-database mr-3 text-blue-400"></i>
                        健康数据结构
                    </h3>
                    <div class="bg-gray-800 rounded-lg p-4 text-sm">
                        <div class="text-green-400 mb-2">// 健康数据核心结构</div>
                        <div class="text-blue-300">typedef struct {</div>
                        <div class="ml-4 text-gray-300">
                            <div><span class="text-purple-400">int</span> age; <span class="text-gray-500">// 年龄</span>
                            </div>
                            <div><span class="text-purple-400">int</span> gender; <span class="text-gray-500">//
                                    性别</span></div>
                            <div><span class="text-purple-400">float</span> height; <span class="text-gray-500">//
                                    身高</span></div>
                            <div><span class="text-purple-400">float</span> weight; <span class="text-gray-500">//
                                    体重</span></div>
                            <div><span class="text-purple-400">float</span> systolic_bp; <span class="text-gray-500">//
                                    收缩压</span></div>
                            <div><span class="text-purple-400">float</span> diastolic_bp; <span class="text-gray-500">//
                                    舒张压</span></div>
                            <div><span class="text-purple-400">float</span> fasting_glucose; <span
                                    class="text-gray-500">// 空腹血糖</span></div>
                            <div><span class="text-purple-400">float</span> hba1c; <span class="text-gray-500">//
                                    糖化血红蛋白</span></div>
                        </div>
                        <div class="text-blue-300">} HealthData;</div>
                    </div>
                </div>

                <!-- Risk Assessment Structure -->
                <div class="code-card rounded-xl p-6 fade-in">
                    <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3 text-yellow-400"></i>
                        风险评估结构
                    </h3>
                    <div class="bg-gray-800 rounded-lg p-4 text-sm">
                        <div class="text-green-400 mb-2">// 慢性病风险评估结果</div>
                        <div class="text-blue-300">typedef struct {</div>
                        <div class="ml-4 text-gray-300">
                            <div><span class="text-purple-400">int</span> cvd_risk_level; <span class="text-gray-500">//
                                    心血管风险</span></div>
                            <div><span class="text-purple-400">int</span> diabetes_risk; <span class="text-gray-500">//
                                    糖尿病风险</span></div>
                            <div><span class="text-purple-400">int</span> ckd_risk; <span class="text-gray-500">//
                                    慢性肾病风险</span></div>
                            <div><span class="text-purple-400">int</span> oncology_alert; <span class="text-gray-500">//
                                    肿瘤风险标志</span></div>
                            <div><span class="text-purple-400">char</span> advice[512]; <span class="text-gray-500">//
                                    个性化建议</span></div>
                        </div>
                        <div class="text-blue-300">} RiskResult;</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Features -->
    <section id="features-detail" class="py-20 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">作品特色</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Intelligent Assessment -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover fade-in">
                    <div class="text-center mb-6">
                        <i class="fas fa-brain text-6xl" style="color: var(--primary-color)"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-center mb-4">智能化评估</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>多维度健康指标综合分析</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>基于医学标准的评估算法</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>个性化健康建议生成</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>动态风险等级评定</li>
                    </ul>
                </div>

                <!-- Data Visualization -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover fade-in">
                    <div class="text-center mb-6">
                        <i class="fas fa-chart-pie text-6xl" style="color: var(--secondary-color)"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-center mb-4">数据可视化</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>健康指标趋势图表</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>风险评估雷达图</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>直观的数据对比展示</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>交互式图表操作</li>
                    </ul>
                </div>

                <!-- User Experience -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover fade-in">
                    <div class="text-center mb-6">
                        <i class="fas fa-user-friends text-6xl" style="color: var(--primary-color)"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-center mb-4">用户体验</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>简洁直观的操作界面</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>中文本土化设计</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>数据输入智能验证</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>多格式数据导入导出</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Metrics -->
    <section id="metrics" class="py-20 px-6">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">系统性能指标</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center fade-in">
                    <div class="text-6xl font-bold tech-number mb-4">18</div>
                    <h3 class="text-xl font-semibold mb-2">健康指标</h3>
                    <p class="text-gray-600">支持的健康数据类型</p>
                </div>

                <div class="text-center fade-in">
                    <div class="text-6xl font-bold tech-number mb-4">100+</div>
                    <h3 class="text-xl font-semibold mb-2">数据记录</h3>
                    <p class="text-gray-600">最大存储容量</p>
                </div>

                <div class="text-center fade-in">
                    <div class="text-6xl font-bold tech-number mb-4">4</div>
                    <h3 class="text-xl font-semibold mb-2">风险类型</h3>
                    <p class="text-gray-600">慢性病风险评估</p>
                </div>

                <div class="text-center fade-in">
                    <div class="text-6xl font-bold tech-number mb-4">95%</div>
                    <h3 class="text-xl font-semibold mb-2">准确率</h3>
                    <p class="text-gray-600">健康评估精度</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience & Insights -->
    <section id="insights" class="py-20 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">心得体会</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Development Experience -->
                <div class="bg-white rounded-xl p-8 shadow-lg fade-in">
                    <h3 class="text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-code mr-4" style="color: var(--primary-color)"></i>
                        开发经验
                    </h3>
                    <div class="space-y-4 text-gray-700">
                        <p><strong>模块化设计：</strong>采用模块化的代码结构，将不同功能分离到独立的文件中，提高了代码的可维护性和可扩展性。</p>
                        <p><strong>数据结构优化：</strong>设计了合理的数据结构来存储健康信息，使用结构体来组织复杂的健康数据，提高了数据处理效率。</p>
                        <p><strong>算法实现：</strong>实现了基于医学标准的健康评估算法，包括BMI计算、血压分级、血糖评估等核心功能。</p>
                        <p><strong>用户体验：</strong>注重用户界面的友好性，提供了清晰的操作提示和错误处理机制。</p>
                    </div>
                </div>

                <!-- Technical Challenges -->
                <div class="bg-white rounded-xl p-8 shadow-lg fade-in">
                    <h3 class="text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-lightbulb mr-4" style="color: var(--secondary-color)"></i>
                        技术挑战
                    </h3>
                    <div class="space-y-4 text-gray-700">
                        <p><strong>数据验证：</strong>实现了完善的数据输入验证机制，确保用户输入的健康数据在合理范围内。</p>
                        <p><strong>文件操作：</strong>掌握了C语言的文件读写操作，实现了数据的持久化存储和CSV格式的导入导出。</p>
                        <p><strong>内存管理：</strong>合理管理内存分配，避免内存泄漏，确保程序的稳定运行。</p>
                        <p><strong>跨平台兼容：</strong>考虑了不同操作系统的兼容性问题，使用了标准的C库函数。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Future Outlook -->
    <section id="future" class="py-20 px-6">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">总结与展望</h2>

            <div class="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-12 fade-in">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <!-- Summary -->
                    <div>
                        <h3 class="text-3xl font-bold mb-6 flex items-center">
                            <i class="fas fa-check-circle mr-4 text-green-500"></i>
                            项目总结
                        </h3>
                        <div class="space-y-4 text-gray-700">
                            <p>本项目成功实现了一个功能完整的个人健康数据管理系统，具备了数据录入、健康评估、风险分析、趋势监测等核心功能。</p>
                            <p>系统采用模块化设计，代码结构清晰，易于维护和扩展。通过合理的数据结构设计和算法实现，确保了系统的高效性和准确性。</p>
                            <p>项目充分体现了C语言在系统开发中的优势，包括高效的内存管理、灵活的数据结构操作和强大的文件处理能力。</p>
                        </div>
                    </div>

                    <!-- Future Plans -->
                    <div>
                        <h3 class="text-3xl font-bold mb-6 flex items-center">
                            <i class="fas fa-rocket mr-4" style="color: var(--primary-color)"></i>
                            未来展望
                        </h3>
                        <div class="space-y-4 text-gray-700">
                            <p><strong>功能扩展：</strong>计划添加更多健康指标的监测，如心率变异性、睡眠质量评估等。</p>
                            <p><strong>算法优化：</strong>引入机器学习算法，提高健康风险预测的准确性和个性化程度。</p>
                            <p><strong>界面升级：</strong>开发图形化用户界面，提供更直观的数据可视化展示。</p>
                            <p><strong>云端集成：</strong>实现数据云端同步，支持多设备访问和数据备份。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- References -->
    <section id="references" class="py-20 px-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 fade-in">参考文献</h2>

            <div class="bg-white rounded-xl p-8 shadow-lg fade-in">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-2xl font-bold mb-6 flex items-center">
                            <i class="fas fa-book mr-3" style="color: var(--primary-color)"></i>
                            学术文献
                        </h3>
                        <ol class="space-y-3 text-gray-700">
                            <li>[1] 中华医学会糖尿病学分会. 中国2型糖尿病防治指南(2020年版)[J]. 中华糖尿病杂志, 2021.</li>
                            <li>[2] 中华医学会心血管病学分会. 中国心血管病预防指南(2017)[J]. 中华心血管病杂志, 2018.</li>
                            <li>[3] WHO. Global Health Observatory data repository. Body mass index (BMI). Geneva: World
                                Health Organization, 2020.</li>
                            <li>[4] American Heart Association. Understanding Blood Pressure Readings. Dallas: AHA,
                                2021.</li>
                        </ol>
                    </div>

                    <div>
                        <h3 class="text-2xl font-bold mb-6 flex items-center">
                            <i class="fas fa-code mr-3" style="color: var(--secondary-color)"></i>
                            技术参考
                        </h3>
                        <ol class="space-y-3 text-gray-700">
                            <li>[5] Kernighan, B. W., & Ritchie, D. M. The C Programming Language (2nd ed.). Prentice
                                Hall, 1988.</li>
                            <li>[6] ISO/IEC 9899:2018. Information technology — Programming languages — C. International
                                Organization for Standardization.</li>
                            <li>[7] Stevens, W. R. Advanced Programming in the UNIX Environment (3rd ed.).
                                Addison-Wesley, 2013.</li>
                            <li>[8] Framingham Heart Study. Cardiovascular Disease Risk Assessment Tools. Framingham,
                                MA, 2020.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-6 text-center">
            <h3 class="text-2xl font-bold mb-4">个人健康数据管理系统</h3>
            <p class="text-gray-400 mb-6">Personal Health Data Management System</p>
            <div class="flex justify-center space-x-6 mb-6">
                <i class="fas fa-heartbeat text-2xl text-red-400"></i>
                <i class="fas fa-chart-line text-2xl" style="color: var(--primary-color)"></i>
                <i class="fas fa-shield-alt text-2xl" style="color: var(--secondary-color)"></i>
            </div>
            <p class="text-gray-500">&copy; 2025 健康数据管理系统. 基于C语言开发的智能健康管理解决方案.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#00AEEF',
                primaryTextColor: '#333',
                primaryBorderColor: '#00AEEF',
                lineColor: '#666',
                secondaryColor: '#4CAF50',
                tertiaryColor: '#f9f9f9'
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.addEventListener('DOMContentLoaded', () => {
            const fadeElements = document.querySelectorAll('.fade-in');
            fadeElements.forEach(el => observer.observe(el));
        });

        // Smooth scrolling function
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Add scroll-triggered animations for numbers
        function animateNumbers() {
            const numbers = document.querySelectorAll('.tech-number');
            numbers.forEach(number => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const target = entry.target;
                            const text = target.textContent;

                            // Extract number from text
                            const match = text.match(/\d+/);
                            if (match) {
                                const finalNumber = parseInt(match[0]);
                                animateCounter(target, 0, finalNumber, 2000, text);
                            }
                            observer.unobserve(target);
                        }
                    });
                });
                observer.observe(number);
            });
        }

        // Counter animation function
        function animateCounter(element, start, end, duration, originalText) {
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const current = Math.floor(start + (end - start) * progress);
                element.textContent = originalText.replace(/\d+/, current);

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // Initialize animations when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            animateNumbers();

            // Add hover effects to cards
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-10px)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0)';
                });
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('section');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Highlight.js initialization
        document.addEventListener('DOMContentLoaded', (event) => {
            hljs.highlightAll();
        });
    </script>
</body>

</html>